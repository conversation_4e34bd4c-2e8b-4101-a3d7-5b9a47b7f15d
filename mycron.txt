# Fixed cron jobs with correct PHP path
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
SHELL=/bin/bash


# Web application cron jobs
# * * * * * /usr/local/bin/php -d "disable_functions=" /var/www/html/Framework/console workflow:maintain	
# * * * * * /usr/local/bin/php -d "disable_functions=" /var/www/html/Framework/console emailsqueue:dispatch	
# * * * * * /usr/local/bin/php -d "disable_functions=" /var/www/html/Framework/console services:cleanup_drafts	
# 1 0 * * * /usr/local/bin/php -d "disable_functions=" /var/www/html/Framework/console media:maintain	
# 3 0 * * * /usr/local/bin/php -d "disable_functions=" /var/www/html/Framework/console backup:clear	
# 5 0 * * * /usr/local/bin/php -d "disable_functions=" /var/www/html/Framework/console errors:maintain	
# 7 0 * * * /usr/local/bin/php /usr/local/bin/php -d "disable_functions=" /var/www/html/Framework/console forms:hide	
# 9 0 * * * /usr/local/bin/php -d "disable_functions=" /var/www/html/Framework/console users:maintain	
# 0 1 * * * /usr/local/bin/php -d "disable_functions=" /var/www/html/Framework/console general:import_update_info	
# 3 1 * * * /usr/local/bin/php -d "disable_functions=" /var/www/html/Framework/console logger:maintain


# test scripts
* * * * * /usr/local/bin/php /var/www/html/scripts/cron-test.php

* * * * * /usr/local/bin/php /var/www/html/Framework/console workflow:maintain	
* * * * * /usr/local/bin/php /var/www/html/Framework/console emailsqueue:dispatch	
* * * * * /usr/local/bin/php /var/www/html/Framework/console services:cleanup_drafts
