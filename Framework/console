#!/usr/bin/env php
<?php
/**
 * Skirta darbui per cli (komandinę eilutę)
 */

use Framework\Tools\Console\CommandLoader;
use Symfony\Component\Console\Application;
use Symfony\Contracts\EventDispatcher\Event;
use Systemsight\Framework\Libraries\Registry;

require_once(__DIR__ . DIRECTORY_SEPARATOR . 'defines.php');
require_once(FW_ROOT . DIRECTORY_SEPARATOR . 'config.php');
require_once(FW_FRAMEWORK . DIRECTORY_SEPARATOR . 'debug.php');
require_once FW_FRAMEWORK . DIRECTORY_SEPARATOR . 'Tools/autoload.php';


//reikalingas, kad Framework
//Registry::$eventDispatcher = Registry::eventDispatcher();
//Registry::$eventDispatcher->dispatch(new Event(), 'system.after_init');

$consoleApplication = new Application();

$commandLoader = new CommandLoader();

$consoleApplication->setCommandLoader($commandLoader);

$consoleApplication->run();
 