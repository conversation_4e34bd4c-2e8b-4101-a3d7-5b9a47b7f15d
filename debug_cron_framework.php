<?php
/**
 * Debug script to test framework initialization from cron
 */

// Log everything to a file
$logFile = '/var/www/html/logs/cron_debug.log';
$log = function($message) use ($logFile) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
};

$log("=== CRON DEBUG START ===");
$log("Current working directory: " . getcwd());
$log("PHP version: " . PHP_VERSION);
$log("Script: " . __FILE__);

// Check environment
$log("Environment variables:");
$log("PATH: " . ($_ENV['PATH'] ?? 'NOT SET'));
$log("USER: " . ($_ENV['USER'] ?? 'NOT SET'));
$log("HOME: " . ($_ENV['HOME'] ?? 'NOT SET'));

// Check if files exist
$consolePath = '/var/www/html/Framework/console';
$log("Console file exists: " . (file_exists($consolePath) ? 'YES' : 'NO'));
$log("Console file readable: " . (is_readable($consolePath) ? 'YES' : 'NO'));

// Check if we can include framework files
$definesPath = '/var/www/html/Framework/defines.php';
$log("Defines file exists: " . (file_exists($definesPath) ? 'YES' : 'NO'));

$configPath = '/var/www/html/config.php';
$log("Config file exists: " . (file_exists($configPath) ? 'YES' : 'NO'));

// Try to run the console command and capture output
$log("Attempting to run console command...");

// Change to the correct directory
chdir('/var/www/html');
$log("Changed working directory to: " . getcwd());

// Check if shell_exec is available
$log("shell_exec available: " . (function_exists('shell_exec') ? 'YES' : 'NO'));

// Try to include framework files directly
$log("Attempting to include framework files...");

try {
    require_once('/var/www/html/Framework/defines.php');
    $log("defines.php included successfully");
} catch (Exception $e) {
    $log("Error including defines.php: " . $e->getMessage());
}

try {
    require_once('/var/www/html/config.php');
    $log("config.php included successfully");
} catch (Exception $e) {
    $log("Error including config.php: " . $e->getMessage());
}

// Check database connection settings
$log("Database settings:");
$log("DATABASE_HOST: " . (defined('DATABASE_HOST') ? DATABASE_HOST : 'NOT DEFINED'));
$log("DATABASE_NAME: " . (defined('DATABASE_NAME') ? DATABASE_NAME : 'NOT DEFINED'));
$log("DATABASE_USERNAME: " . (defined('DATABASE_USERNAME') ? DATABASE_USERNAME : 'NOT DEFINED'));

$log("=== CRON DEBUG END ===");

echo "Debug completed. Check /var/www/html/logs/cron_debug.log\n";
?>
