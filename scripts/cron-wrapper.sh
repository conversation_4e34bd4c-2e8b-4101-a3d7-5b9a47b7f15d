#!/bin/bash

# Set up environment for cron jobs
export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
export HOME="/root"
export USER="root"

# Change to the correct directory
cd /var/www/html

# Log the execution
echo "[$(date)] Running: $@" >> /var/www/html/logs/cron-wrapper.log

# Execute the command and log any errors
/usr/local/bin/php "$@" 2>&1 | tee -a /var/www/html/logs/cron-wrapper.log

# Log completion
echo "[$(date)] Completed: $@" >> /var/www/html/logs/cron-wrapper.log
